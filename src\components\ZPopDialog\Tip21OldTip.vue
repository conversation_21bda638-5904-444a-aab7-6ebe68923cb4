<template>
  <ZPopOverlay :show="showTips21Tip" :lock-scroll="false">
    <div class="content" @click.stop>
      <div class="title">Responsible Gaming</div>
      <div class="scroll" @click.stop>
        <div class="sub-title">IMPORTANT NOTICE</div>
        <ul class="notice-list">
          <p class="title-desc">By clicking "I agree all", you confirm that you are:</p>
          <li>Over 21 years old.</li>
          <li>Not a government official.</li>
          <li>Not a Gaming Employment License (GEL) holder.</li>
          <li>Not a member of the Philippine Armed Forces or National Police.</li>
          <li>Not on PAGCOR's National Database of Restricted Persons (NDRP).</li>
          <li>Not playing in public or open places.</li>
        </ul>
        <div class="question-section">
          <ZIcon type="icon-warn" color="#FF936F" :size="18" class="icon"></ZIcon>
          Funds or credits on the account of player who is found ineligible to play shall mean
          forfeiture of said funds/credits in favor of the Government.
        </div>
      </div>
      <div class="about-us">
        <ProviderLogo></ProviderLogo>
      </div>
      <div class="action-buttons">
        <ZButton type="default" @click="handleClose">Exit</ZButton>
        <ZButton @click="handleClose">I Agree All</ZButton>
      </div>
    </div>
  </ZPopOverlay>
</template>
<script lang="ts" setup>
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import ProviderLogo from "@/components/ProviderLogo.vue";

import { AutoPopMgr } from "@/utils/AutoPopMgr";
const autoPopMgrStore = useAutoPopMgrStore();
const { showTips21Tip } = storeToRefs(autoPopMgrStore);

const handleClose = () => {
  showTips21Tip.value = false;
  AutoPopMgr.destroyCurrentPopup();
};
</script>
<style lang="scss" scoped>
.content {
  width: 327px;
  overflow: auto;
  box-sizing: border-box;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  flex-direction: column;
  -webkit-overflow-scrolling: touch;

  .title {
    color: #000;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 100%;
    margin-bottom: 10px;
  }

  .sub-title {
    margin-bottom: 10px;
    text-align: center;
  }

  .scroll {
    height: 320px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    touch-action: auto;
    scroll-behavior: smooth;
    /* 恢复子元素的滚动 */
  }

  .title-desc {
    margin-bottom: 8px;
    color: #4f6477;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .notice-list {
    border-radius: 8px;
    text-align: left;
    border-radius: 12px;
    background: #f9f9f9;
    display: flex;
    flex-direction: column;
    padding: 8px 12px;
    justify-content: space-between;
    align-items: flex-start;
    align-self: stretch;

    li {
      margin-bottom: 4px;
      color: #4f6477;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
      position: relative;
      padding-left: 15px;

      &:before {
        content: "";
        position: absolute;
        width: 5px;
        height: 5px;
        background-color: #4f6477;
        border-radius: 50%;
        left: 5px;
        top: 6px;
      }
    }
  }

  .about-us {
    width: 100%;
    display: flex;
    margin: 12px auto;
  }

  .question-section {
    margin-top: 10px;
    margin-bottom: 10px;
    color: #444;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    display: flex;
    background-color: #ffefba;
    border-radius: 10px;
    padding: 10px;

    .icon {
      margin-right: 4px;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-bottom: 10px;
  width: 100%;

  &:deep(.van-button__text) {
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }

  button {
    font-weight: 600;
  }
}
</style>
